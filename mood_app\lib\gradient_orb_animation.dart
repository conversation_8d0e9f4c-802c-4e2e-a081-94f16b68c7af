import 'dart:math' as math;
import 'package:flutter/material.dart';

class GradientOrbAnimation extends StatefulWidget {
  final double size;

  const GradientOrbAnimation({
    super.key,
    this.size = 300,
  });

  @override
  State<GradientOrbAnimation> createState() => _GradientOrbAnimationState();
}

class _GradientOrbAnimationState extends State<GradientOrbAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  
  final List<Color> _colors = [
    const Color(0xAAB388FF), // Purple
    const Color(0xAAE1BEE7), // Light purple
    const Color(0xAAF48FB1), // Pink
    const Color(0xAABBDEFB), // Light blue
    const Color(0xAAD1C4E9), // Lavender
  ];

  @override
  void initState() {
    super.initState();
    
    // Create multiple animation controllers for different circles
    _controllers = List.generate(
      5,
      (index) => AnimationController(
        vsync: this,
        duration: Duration(milliseconds: 3000 + (index * 500)),
      ),
    );
    
    // Create animations with different curves
    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeInOut,
        ),
      );
    }).toList();
    
    // Start the animations with different delays
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        _controllers[i].repeat(reverse: true);
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: List.generate(_controllers.length, (index) {
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              return Positioned(
                left: widget.size / 2 - (widget.size * 0.4),
                top: widget.size / 2 - (widget.size * 0.4),
                child: Transform.translate(
                  offset: Offset(
                    math.sin(_animations[index].value * 2 * math.pi) * 10 * (index + 1),
                    math.cos(_animations[index].value * 2 * math.pi) * 10 * (index + 1),
                  ),
                  child: Opacity(
                    opacity: 0.7,
                    child: Container(
                      width: widget.size * 0.8,
                      height: widget.size * 0.8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            _colors[index],
                            _colors[index].withOpacity(0.3),
                          ],
                          stops: const [0.2, 1.0],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}

class GlassyOrbAnimation extends StatefulWidget {
  final double size;

  const GlassyOrbAnimation({
    super.key,
    this.size = 300,
  });

  @override
  State<GlassyOrbAnimation> createState() => _GlassyOrbAnimationState();
}

class _GlassyOrbAnimationState extends State<GlassyOrbAnimation>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    );
    
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    );
    
    _rotationAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear)
    );
    
    _pulseAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut)
    );
    
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: Listenable.merge([_rotationAnimation, _pulseAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value * 2 * math.pi,
              child: CustomPaint(
                size: Size(widget.size, widget.size),
                painter: GlassyOrbPainter(),
              ),
            ),
          );
        },
      ),
    );
  }
}

class GlassyOrbPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.45;
    
    // Define colors for the orbs
    final colors = [
      const Color(0xAAB388FF), // Purple
      const Color(0xAAE1BEE7), // Light purple
      const Color(0xAAF48FB1), // Pink
      const Color(0xAABBDEFB), // Light blue
    ];
    
    // Draw multiple overlapping translucent circles
    for (int i = 0; i < colors.length; i++) {
      final offset = Offset(
        math.cos(i * math.pi / 2) * (radius * 0.2),
        math.sin(i * math.pi / 2) * (radius * 0.2),
      );
      
      final paint = Paint()
        ..color = colors[i]
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);
      
      canvas.drawCircle(center + offset, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
