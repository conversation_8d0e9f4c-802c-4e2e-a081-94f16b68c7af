import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class CenterLottieAnimation extends StatefulWidget {
  final double size;
  
  const CenterLottieAnimation({
    super.key,
    this.size = 60,
  });

  @override
  State<CenterLottieAnimation> createState() => _CenterLottieAnimationState();
}

class _CenterLottieAnimationState extends State<CenterLottieAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    // Start the animation
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: Lottie.network(
        'https://lottie.host/46ab9c49-0be8-4d30-8b62-23ad1bad57a7/kxXM5iSzLE.json',
        controller: _controller,
        repeat: true,
        animate: true,
        fit: BoxFit.cover,
        frameRate: FrameRate.max,
        onLoaded: (composition) {
          // Update the controller duration to match the animation
          _controller.duration = composition.duration;
          _controller.repeat();
        },
        errorBuilder: (context, error, stackTrace) {
          // Fallback to the original purple circle if animation fails to load
          return Container(
            width: widget.size,
            height: widget.size,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Color(0xFF9C27B0),
            ),
          );
        },
      ),
    );
  }
}
