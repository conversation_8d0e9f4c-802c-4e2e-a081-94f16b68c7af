import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math';
import 'app_router.dart';
import 'budget_input_page.dart';
import 'center_lottie_animation.dart';

void main() => runApp(const MoodApp());

class MoodApp extends StatelessWidget {
  const MoodApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: const MoodHomePage(),
      theme: ThemeData.dark(),
      onGenerateRoute: AppRouter.generateRoute,
    );
  }
}

class MoodHomePage extends StatefulWidget {
  const MoodHomePage({super.key});

  @override
  State<MoodHomePage> createState() => _MoodHomePageState();
}

class _MoodHomePageState extends State<MoodHomePage>
    with TickerProviderStateMixin {
  String? selectedMood;
  bool showBottom = false;
  late AnimationController _animationController;
  late List<AnimationController> _moodControllers;
  late List<Animation<double>> _moodScaleAnimations;
  late List<Animation<double>> _moodGlowAnimations;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Initialize mood animation controllers
    _moodControllers = List.generate(
      moods.length,
      (index) => AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 400),
      ),
    );

    // Initialize scale animations
    _moodScaleAnimations = _moodControllers.map((controller) {
      return Tween<double>(begin: 1.0, end: 1.3).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();

    // Initialize glow animations
    _moodGlowAnimations = _moodControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (var controller in _moodControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  final List<Map<String, dynamic>> moods = [
    {"emoji": "😢", "label": "Sad", "color": Color(0xFF64B5F6)},
    {"emoji": "😰", "label": "Anxious", "color": Color(0xFFFF7043)},
    {"emoji": "😄", "label": "Happy", "color": Color(0xFFFFD54F)},
    {"emoji": "😌", "label": "Relaxed", "color": Color(0xFF81C784)},
    {"emoji": "⚡", "label": "Energetic", "color": Color(0xFFE91E63)},
    {"emoji": "🥱", "label": "Bored", "color": Color(0xFF9575CD)},
  ];

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final time = TimeOfDay.now().format(context);
    final day = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday"
    ][now.weekday % 7];

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF2D2D3A), Color(0xFF1A1A24)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          RichText(
                            text: TextSpan(
                              style: GoogleFonts.poppins(
                                  fontSize: 32,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.white),
                              children: const [
                                TextSpan(text: "Hi, "),
                                TextSpan(
                                  text: "There",
                                  style: TextStyle(color: Color(0xFFFF6EC7)),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text("Good Morning!",
                              style: GoogleFonts.poppins(
                                  color: Colors.white70,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w300)),
                        ],
                      ),
                      Text(time,
                          style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w300)),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Center(
                    child: Column(
                      children: [
                        Text(
                          day,
                          style: GoogleFonts.poppins(
                              fontSize: 20,
                              color: Colors.white,
                              fontWeight: FontWeight.w300),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.network(
                              'https://openweathermap.org/img/wn/<EMAIL>',
                              width: 30,
                              height: 30,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Icon(Icons.cloud,
                                      color: Colors.white70, size: 24),
                            ),
                            const SizedBox(width: 6),
                            Text("38° / 12°",
                                style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w300)),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 60),
                  Center(
                    child: Text(
                      "how are you feeling today?",
                      style: GoogleFonts.poppins(
                        fontSize: 22,
                        color: Colors.white,
                        fontWeight: FontWeight.w300,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                  Center(
                    child: Container(
                      width: 280,
                      height: 280,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black12,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(50),
                            blurRadius: 15,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Center Lottie animation
                          const CenterLottieAnimation(
                            size: 60,
                          ),
                          // Mood emojis positioned in a circle
                          ...List.generate(moods.length, (index) {
                            final double angle =
                                (index * (2 * pi / moods.length));
                            const double radius = 100.0;
                            final double x = radius * cos(angle);
                            final double y = radius * sin(angle);

                            return Positioned(
                              left: 140 + x - 30,
                              top: 140 + y - 30,
                              child: GestureDetector(
                                onTap: () {
                                  // Trigger mood animation
                                  _moodControllers[index].forward().then((_) {
                                    _moodControllers[index].reverse();
                                  });

                                  setState(() {
                                    selectedMood = moods[index]['label'];
                                    showBottom = true;
                                    _animationController.reset();
                                    _animationController.forward();
                                  });
                                },
                                child: AnimatedBuilder(
                                  animation: Listenable.merge([
                                    _moodScaleAnimations[index],
                                    _moodGlowAnimations[index],
                                  ]),
                                  builder: (context, child) {
                                    return Transform.scale(
                                      scale: _moodScaleAnimations[index].value,
                                      child: Column(
                                        children: [
                                          Container(
                                            width: 60,
                                            height: 60,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: selectedMood ==
                                                      moods[index]['label']
                                                  ? moods[index]['color']
                                                      .withValues(alpha: 0.2)
                                                  : Colors.black26,
                                              border: selectedMood ==
                                                      moods[index]['label']
                                                  ? Border.all(
                                                      color: moods[index]
                                                          ['color'],
                                                      width: 2,
                                                    )
                                                  : null,
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withValues(alpha: 0.2),
                                                  blurRadius: 5,
                                                  spreadRadius: 1,
                                                ),
                                                // Animated glow effect
                                                BoxShadow(
                                                  color: moods[index]['color']
                                                      .withValues(
                                                    alpha: _moodGlowAnimations[
                                                                index]
                                                            .value *
                                                        0.6,
                                                  ),
                                                  blurRadius: 20 *
                                                      _moodGlowAnimations[index]
                                                          .value,
                                                  spreadRadius: 5 *
                                                      _moodGlowAnimations[index]
                                                          .value,
                                                ),
                                              ],
                                            ),
                                            child: Center(
                                              child:
                                                  TweenAnimationBuilder<double>(
                                                duration: const Duration(
                                                    milliseconds: 200),
                                                tween: Tween(
                                                  begin: 1.0,
                                                  end: selectedMood ==
                                                          moods[index]['label']
                                                      ? 1.2
                                                      : 1.0,
                                                ),
                                                builder:
                                                    (context, scale, child) {
                                                  return Transform.scale(
                                                    scale: scale,
                                                    child: Text(
                                                      moods[index]['emoji']!,
                                                      style: const TextStyle(
                                                          fontSize: 28),
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 6),
                                          Text(
                                            moods[index]['label']!,
                                            style: GoogleFonts.poppins(
                                              color: selectedMood ==
                                                      moods[index]['label']
                                                  ? moods[index]['color']
                                                  : Colors.white,
                                              fontSize: 12,
                                              fontWeight: selectedMood ==
                                                      moods[index]['label']
                                                  ? FontWeight.w500
                                                  : FontWeight.w300,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    transitionBuilder:
                        (Widget child, Animation<double> animation) {
                      return FadeTransition(
                        opacity: animation,
                        child: ScaleTransition(
                          scale: animation,
                          child: child,
                        ),
                      );
                    },
                    child: showBottom
                        ? Column(
                            key: ValueKey(selectedMood),
                            children: [
                              Center(
                                child: Icon(
                                  Icons.keyboard_arrow_down,
                                  color: Colors.white.withAlpha(128),
                                  size: 24,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text.rich(
                                TextSpan(
                                  text: "You're feeling: ",
                                  style: GoogleFonts.poppins(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w300),
                                  children: [
                                    TextSpan(
                                      text: selectedMood,
                                      style: const TextStyle(
                                          color: Color(0xFFB388FF),
                                          fontWeight: FontWeight.w500),
                                    )
                                  ],
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  foregroundColor: Colors.black87,
                                  elevation: 0,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 40, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(30)),
                                ),
                                onPressed: () {
                                  // Use direct navigation with MaterialPageRoute as a fallback
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          const BudgetInputPage(),
                                    ),
                                  );
                                },
                                child: Text(
                                  "Tap to continue",
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : const SizedBox.shrink(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
