import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import '../models/user_data.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  // Sample data - in a real app, this would be stored in local storage or database
  UserProfile? _userProfile;
  List<Activity> _availableActivities = [];
  List<Achievement> _availableAchievements = [];

  // Initialize with sample data
  void initialize() {
    _initializeActivities();
    _initializeAchievements();
    _initializeUserProfile();
  }

  void _initializeActivities() {
    _availableActivities = [
      // Physical Activities
      Activity(
        id: 'phy_001',
        name: 'Morning Yoga',
        category: 'Physical',
        description: 'Start your day with gentle yoga stretches',
        tags: ['morning', 'relaxation', 'flexibility'],
      ),
      Activity(
        id: 'phy_002',
        name: 'Nature Walk',
        category: 'Physical',
        description: 'Take a peaceful walk in a nearby park',
        tags: ['outdoor', 'nature', 'exercise'],
      ),
      Activity(
        id: 'phy_003',
        name: 'Dance Class',
        category: 'Physical',
        description: 'Learn a new dance style or join a class',
        tags: ['social', 'music', 'exercise'],
      ),
      Activity(
        id: 'phy_004',
        name: 'Rock Climbing',
        category: 'Physical',
        description: 'Try indoor rock climbing for an adrenaline rush',
        tags: ['adventure', 'strength', 'challenge'],
      ),
      Activity(
        id: 'phy_005',
        name: 'Swimming',
        category: 'Physical',
        description: 'Enjoy a refreshing swim at a local pool',
        tags: ['water', 'cardio', 'relaxation'],
      ),

      // Creative Activities
      Activity(
        id: 'cre_001',
        name: 'Watercolor Painting',
        category: 'Creative',
        description: 'Express yourself through watercolor art',
        tags: ['art', 'creativity', 'relaxation'],
      ),
      Activity(
        id: 'cre_002',
        name: 'Learn Guitar',
        category: 'Creative',
        description: 'Start learning to play the guitar',
        tags: ['music', 'skill', 'creativity'],
      ),
      Activity(
        id: 'cre_003',
        name: 'Photography Walk',
        category: 'Creative',
        description: 'Explore your city with a camera',
        tags: ['art', 'outdoor', 'exploration'],
      ),
      Activity(
        id: 'cre_004',
        name: 'Pottery Class',
        category: 'Creative',
        description: 'Create beautiful pottery with your hands',
        tags: ['art', 'hands-on', 'meditation'],
      ),
      Activity(
        id: 'cre_005',
        name: 'Creative Writing',
        category: 'Creative',
        description: 'Write a short story or poem',
        tags: ['writing', 'imagination', 'reflection'],
      ),

      // Social Activities
      Activity(
        id: 'soc_001',
        name: 'Coffee with a Friend',
        category: 'Social',
        description: 'Catch up with a friend over coffee',
        tags: ['social', 'conversation', 'relaxation'],
      ),
      Activity(
        id: 'soc_002',
        name: 'Join a Book Club',
        category: 'Social',
        description: 'Meet new people who love reading',
        tags: ['social', 'reading', 'discussion'],
      ),
      Activity(
        id: 'soc_003',
        name: 'Volunteer Work',
        category: 'Social',
        description: 'Help others in your community',
        tags: ['helping', 'community', 'purpose'],
      ),
      Activity(
        id: 'soc_004',
        name: 'Game Night',
        category: 'Social',
        description: 'Host or join a board game night',
        tags: ['social', 'fun', 'games'],
      ),

      // Learning Activities
      Activity(
        id: 'lea_001',
        name: 'Learn a New Language',
        category: 'Learning',
        description: 'Start learning a foreign language',
        tags: ['education', 'skill', 'culture'],
      ),
      Activity(
        id: 'lea_002',
        name: 'Online Course',
        category: 'Learning',
        description: 'Take an online course in something interesting',
        tags: ['education', 'skill', 'growth'],
      ),
      Activity(
        id: 'lea_003',
        name: 'Museum Visit',
        category: 'Learning',
        description: 'Explore a local museum or gallery',
        tags: ['culture', 'learning', 'art'],
      ),
      Activity(
        id: 'lea_004',
        name: 'Cooking Class',
        category: 'Learning',
        description: 'Learn to cook a new cuisine',
        tags: ['cooking', 'skill', 'culture'],
      ),

      // Relaxation Activities
      Activity(
        id: 'rel_001',
        name: 'Meditation Session',
        category: 'Relaxation',
        description: 'Practice mindfulness meditation',
        tags: ['mindfulness', 'peace', 'mental-health'],
      ),
      Activity(
        id: 'rel_002',
        name: 'Spa Day',
        category: 'Relaxation',
        description: 'Treat yourself to a relaxing spa day',
        tags: ['self-care', 'relaxation', 'pampering'],
      ),
      Activity(
        id: 'rel_003',
        name: 'Stargazing',
        category: 'Relaxation',
        description: 'Spend an evening looking at the stars',
        tags: ['nature', 'peaceful', 'wonder'],
      ),
      Activity(
        id: 'rel_004',
        name: 'Reading in the Park',
        category: 'Relaxation',
        description: 'Find a quiet spot and read a good book',
        tags: ['reading', 'nature', 'peaceful'],
      ),

      // Adventure Activities
      Activity(
        id: 'adv_001',
        name: 'Geocaching',
        category: 'Adventure',
        description: 'Go on a modern treasure hunt',
        tags: ['adventure', 'outdoor', 'exploration'],
      ),
      Activity(
        id: 'adv_002',
        name: 'Try a New Restaurant',
        category: 'Adventure',
        description: 'Explore a cuisine you\'ve never tried',
        tags: ['food', 'culture', 'exploration'],
      ),
      Activity(
        id: 'adv_003',
        name: 'Day Trip',
        category: 'Adventure',
        description: 'Visit a nearby town or attraction',
        tags: ['travel', 'exploration', 'adventure'],
      ),
      Activity(
        id: 'adv_004',
        name: 'Escape Room',
        category: 'Adventure',
        description: 'Challenge yourself with an escape room',
        tags: ['puzzle', 'challenge', 'social'],
      ),
    ];
  }

  void _initializeAchievements() {
    _availableAchievements = [
      Achievement(
        id: 'ach_001',
        title: 'First Step',
        description: 'Complete your first activity',
        icon: '🎯',
        requiredCount: 1,
      ),
      Achievement(
        id: 'ach_002',
        title: 'Explorer',
        description: 'Try 5 different activities',
        icon: '🗺️',
        requiredCount: 5,
      ),
      Achievement(
        id: 'ach_003',
        title: 'Adventurer',
        description: 'Complete 10 activities',
        icon: '🏔️',
        requiredCount: 10,
      ),
      Achievement(
        id: 'ach_004',
        title: 'Mood Master',
        description: 'Log your mood for 7 consecutive days',
        icon: '📊',
        requiredCount: 7,
      ),
      Achievement(
        id: 'ach_005',
        title: 'Creative Soul',
        description: 'Complete 3 creative activities',
        icon: '🎨',
        requiredCount: 3,
      ),
      Achievement(
        id: 'ach_006',
        title: 'Social Butterfly',
        description: 'Complete 3 social activities',
        icon: '🦋',
        requiredCount: 3,
      ),
      Achievement(
        id: 'ach_007',
        title: 'Wellness Warrior',
        description: 'Complete 5 physical activities',
        icon: '💪',
        requiredCount: 5,
      ),
      Achievement(
        id: 'ach_008',
        title: 'Lifelong Learner',
        description: 'Complete 3 learning activities',
        icon: '📚',
        requiredCount: 3,
      ),
      Achievement(
        id: 'ach_009',
        title: 'Zen Master',
        description: 'Complete 5 relaxation activities',
        icon: '🧘',
        requiredCount: 5,
      ),
      Achievement(
        id: 'ach_010',
        title: 'Legend',
        description: 'Complete 25 activities',
        icon: '👑',
        requiredCount: 25,
      ),
    ];
  }

  void _initializeUserProfile() {
    _userProfile = UserProfile(
      name: 'User',
      moodHistory: _generateSampleMoodHistory(),
      completedActivities: [],
      achievements: _availableAchievements,
      preferences: {
        'notifications': true,
        'reminderTime': '09:00',
        'preferredCategories': ['Physical', 'Creative'],
      },
      totalActivitiesTried: 0,
      currentStreak: 0,
    );
  }

  List<MoodEntry> _generateSampleMoodHistory() {
    final List<MoodEntry> history = [];
    final Random random = Random();
    final List<String> moods = [
      'Sad',
      'Anxious',
      'Happy',
      'Relaxed',
      'Energetic',
      'Bored'
    ];
    final List<String> emojis = ['😢', '😰', '😄', '😌', '⚡', '🥱'];
    final List<double> moodValues = [2.0, 3.0, 8.0, 7.0, 9.0, 4.0];

    for (int i = 7; i >= 0; i--) {
      final date = DateTime.now().subtract(Duration(days: i));
      final moodIndex = random.nextInt(moods.length);

      history.add(MoodEntry(
        date: date,
        mood: moods[moodIndex],
        emoji: emojis[moodIndex],
        moodValue: moodValues[moodIndex] + (random.nextDouble() - 0.5),
        note: _generateSampleNote(moods[moodIndex]),
        activities: _generateSampleActivities(),
        weather: _generateSampleWeather(),
        timeOfDay: TimeOfDay(
          hour: 9 + random.nextInt(12),
          minute: random.nextInt(60),
        ),
      ));
    }

    return history;
  }

  String _generateSampleNote(String mood) {
    final Map<String, List<String>> notes = {
      'Happy': [
        'Had a great day at work!',
        'Spent time with friends',
        'Accomplished my goals',
        'Beautiful weather today'
      ],
      'Sad': [
        'Feeling a bit down today',
        'Missing family',
        'Tough day at work',
        'Feeling lonely'
      ],
      'Anxious': [
        'Big presentation tomorrow',
        'Worried about deadlines',
        'Feeling overwhelmed',
        'Lots on my mind'
      ],
      'Relaxed': [
        'Peaceful evening at home',
        'Good meditation session',
        'Enjoyed a quiet day',
        'Feeling centered'
      ],
      'Energetic': [
        'Ready to take on the world!',
        'Great workout this morning',
        'Feeling motivated',
        'Lots of energy today'
      ],
      'Bored': [
        'Nothing interesting happening',
        'Need something new to do',
        'Feeling restless',
        'Same routine as always'
      ],
    };

    final moodNotes = notes[mood] ?? ['Just another day'];
    return moodNotes[Random().nextInt(moodNotes.length)];
  }

  List<String> _generateSampleActivities() {
    final activities = [
      'work',
      'exercise',
      'reading',
      'cooking',
      'socializing',
      'relaxing'
    ];
    final random = Random();
    final count = 1 + random.nextInt(3);
    final selected = <String>[];

    for (int i = 0; i < count; i++) {
      final activity = activities[random.nextInt(activities.length)];
      if (!selected.contains(activity)) {
        selected.add(activity);
      }
    }

    return selected;
  }

  String _generateSampleWeather() {
    final weathers = ['Sunny', 'Cloudy', 'Rainy', 'Partly Cloudy', 'Clear'];
    return weathers[Random().nextInt(weathers.length)];
  }

  // Getters
  UserProfile? get userProfile => _userProfile;
  List<Activity> get availableActivities => _availableActivities;
  List<Achievement> get availableAchievements => _availableAchievements;

  // Mood Entry Methods
  void addMoodEntry(MoodEntry entry) {
    _userProfile?.moodHistory.add(entry);
    _checkAchievements();
  }

  List<MoodEntry> getMoodHistory({int? days}) {
    if (_userProfile == null) return [];

    if (days == null) return _userProfile!.moodHistory;

    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return _userProfile!.moodHistory
        .where((entry) => entry.date.isAfter(cutoffDate))
        .toList();
  }

  // Activity Methods
  Activity? getRandomUntriedActivity(
      {String? category, List<String>? excludeTags}) {
    final completedIds =
        _userProfile?.completedActivities.map((a) => a.id).toSet() ?? {};

    var available = _availableActivities
        .where((activity) => !completedIds.contains(activity.id))
        .toList();

    if (category != null) {
      available = available.where((a) => a.category == category).toList();
    }

    if (excludeTags != null && excludeTags.isNotEmpty) {
      available = available
          .where((a) => !a.tags.any((tag) => excludeTags.contains(tag)))
          .toList();
    }

    if (available.isEmpty) return null;

    return available[Random().nextInt(available.length)];
  }

  void completeActivity(Activity activity) {
    final completedActivity = activity.copyWith(
      isCompleted: true,
      completedDate: DateTime.now(),
    );

    _userProfile?.completedActivities.add(completedActivity);
    _userProfile = _userProfile?.copyWith(
      totalActivitiesTried: (_userProfile?.totalActivitiesTried ?? 0) + 1,
    );

    _checkAchievements();
  }

  // Achievement Methods
  void _checkAchievements() {
    if (_userProfile == null) return;

    final updatedAchievements = <Achievement>[];

    for (final achievement in _userProfile!.achievements) {
      if (achievement.isUnlocked) {
        updatedAchievements.add(achievement);
        continue;
      }

      bool shouldUnlock = false;

      switch (achievement.id) {
        case 'ach_001': // First Step
          shouldUnlock = _userProfile!.totalActivitiesTried >= 1;
          break;
        case 'ach_002': // Explorer
          shouldUnlock = _userProfile!.totalActivitiesTried >= 5;
          break;
        case 'ach_003': // Adventurer
          shouldUnlock = _userProfile!.totalActivitiesTried >= 10;
          break;
        case 'ach_004': // Mood Master
          shouldUnlock = _checkConsecutiveMoodLogs(7);
          break;
        case 'ach_005': // Creative Soul
          shouldUnlock = _countActivitiesByCategory('Creative') >= 3;
          break;
        case 'ach_006': // Social Butterfly
          shouldUnlock = _countActivitiesByCategory('Social') >= 3;
          break;
        case 'ach_007': // Wellness Warrior
          shouldUnlock = _countActivitiesByCategory('Physical') >= 5;
          break;
        case 'ach_008': // Lifelong Learner
          shouldUnlock = _countActivitiesByCategory('Learning') >= 3;
          break;
        case 'ach_009': // Zen Master
          shouldUnlock = _countActivitiesByCategory('Relaxation') >= 5;
          break;
        case 'ach_010': // Legend
          shouldUnlock = _userProfile!.totalActivitiesTried >= 25;
          break;
      }

      if (shouldUnlock) {
        updatedAchievements.add(Achievement(
          id: achievement.id,
          title: achievement.title,
          description: achievement.description,
          icon: achievement.icon,
          requiredCount: achievement.requiredCount,
          isUnlocked: true,
          unlockedDate: DateTime.now(),
        ));
      } else {
        updatedAchievements.add(achievement);
      }
    }

    _userProfile = _userProfile!.copyWith(achievements: updatedAchievements);
  }

  bool _checkConsecutiveMoodLogs(int days) {
    if (_userProfile!.moodHistory.length < days) return false;

    final sortedHistory =
        _userProfile!.moodHistory.map((e) => e.date).toSet().toList()..sort();

    if (sortedHistory.length < days) return false;

    for (int i = sortedHistory.length - days;
        i < sortedHistory.length - 1;
        i++) {
      final diff = sortedHistory[i + 1].difference(sortedHistory[i]).inDays;
      if (diff > 1) return false;
    }

    return true;
  }

  int _countActivitiesByCategory(String category) {
    return _userProfile!.completedActivities
        .where((activity) =>
            _availableActivities
                .firstWhere((a) => a.id == activity.id,
                    orElse: () => Activity(
                        id: '',
                        name: '',
                        category: '',
                        description: '',
                        tags: []))
                .category ==
            category)
        .length;
  }

  // Smart Suggestions
  List<Activity> getSmartSuggestions() {
    if (_userProfile == null) return [];

    final suggestions = <Activity>[];
    final now = DateTime.now();
    final currentHour = now.hour;
    final dayOfWeek = now.weekday;

    // Time-based suggestions
    if (currentHour >= 6 && currentHour <= 9) {
      // Morning suggestions
      final morningActivity = getRandomUntriedActivity(category: 'Physical');
      if (morningActivity != null) suggestions.add(morningActivity);
    } else if (currentHour >= 12 && currentHour <= 14) {
      // Lunch time suggestions
      final lunchActivity = getRandomUntriedActivity(category: 'Social');
      if (lunchActivity != null) suggestions.add(lunchActivity);
    } else if (currentHour >= 18 && currentHour <= 21) {
      // Evening suggestions
      final eveningActivity = getRandomUntriedActivity(category: 'Relaxation');
      if (eveningActivity != null) suggestions.add(eveningActivity);
    }

    // Mood-based suggestions
    final recentMoods = getMoodHistory(days: 3);
    if (recentMoods.isNotEmpty) {
      final avgMood =
          recentMoods.map((e) => e.moodValue).reduce((a, b) => a + b) /
              recentMoods.length;

      if (avgMood < 5) {
        // Low mood - suggest uplifting activities
        final upliftingActivity =
            getRandomUntriedActivity(category: 'Creative');
        if (upliftingActivity != null &&
            !suggestions.contains(upliftingActivity)) {
          suggestions.add(upliftingActivity);
        }
      } else if (avgMood > 7) {
        // High mood - suggest adventure activities
        final adventureActivity =
            getRandomUntriedActivity(category: 'Adventure');
        if (adventureActivity != null &&
            !suggestions.contains(adventureActivity)) {
          suggestions.add(adventureActivity);
        }
      }
    }

    // Add random suggestion if we don't have enough
    while (suggestions.length < 3) {
      final randomActivity = getRandomUntriedActivity();
      if (randomActivity != null && !suggestions.contains(randomActivity)) {
        suggestions.add(randomActivity);
      } else {
        break;
      }
    }

    return suggestions;
  }

  // Analytics
  Map<String, double> getMoodAnalytics({int days = 30}) {
    final history = getMoodHistory(days: days);
    if (history.isEmpty) return {};

    final moodCounts = <String, int>{};
    for (final entry in history) {
      moodCounts[entry.mood] = (moodCounts[entry.mood] ?? 0) + 1;
    }

    final total = history.length;
    return moodCounts.map((mood, count) => MapEntry(mood, count / total));
  }

  List<MapEntry<String, int>> getTopActivities({int days = 30}) {
    final history = getMoodHistory(days: days);
    final activityCounts = <String, int>{};

    for (final entry in history) {
      for (final activity in entry.activities) {
        activityCounts[activity] = (activityCounts[activity] ?? 0) + 1;
      }
    }

    final sorted = activityCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sorted.take(5).toList();
  }
}

extension UserProfileCopyWith on UserProfile {
  UserProfile copyWith({
    String? name,
    List<MoodEntry>? moodHistory,
    List<Activity>? completedActivities,
    List<Achievement>? achievements,
    Map<String, dynamic>? preferences,
    int? totalActivitiesTried,
    int? currentStreak,
  }) {
    return UserProfile(
      name: name ?? this.name,
      moodHistory: moodHistory ?? this.moodHistory,
      completedActivities: completedActivities ?? this.completedActivities,
      achievements: achievements ?? this.achievements,
      preferences: preferences ?? this.preferences,
      totalActivitiesTried: totalActivitiesTried ?? this.totalActivitiesTried,
      currentStreak: currentStreak ?? this.currentStreak,
    );
  }
}
