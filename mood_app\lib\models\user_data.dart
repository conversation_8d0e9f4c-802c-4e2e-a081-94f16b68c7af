import 'dart:convert';
import 'package:flutter/material.dart';

class MoodEntry {
  final DateTime date;
  final String mood;
  final String emoji;
  final double moodValue; // 1-10 scale
  final String? note;
  final List<String> activities;
  final String? weather;
  final TimeOfDay timeOfDay;

  MoodEntry({
    required this.date,
    required this.mood,
    required this.emoji,
    required this.moodValue,
    this.note,
    required this.activities,
    this.weather,
    required this.timeOfDay,
  });

  Map<String, dynamic> toJson() => {
        'date': date.toIso8601String(),
        'mood': mood,
        'emoji': emoji,
        'moodValue': moodValue,
        'note': note,
        'activities': activities,
        'weather': weather,
        'timeOfDay': '${timeOfDay.hour}:${timeOfDay.minute}',
      };

  factory MoodEntry.fromJson(Map<String, dynamic> json) => MoodEntry(
        date: DateTime.parse(json['date']),
        mood: json['mood'],
        emoji: json['emoji'],
        moodValue: json['moodValue'].toDouble(),
        note: json['note'],
        activities: List<String>.from(json['activities']),
        weather: json['weather'],
        timeOfDay: TimeOfDay(
          hour: int.parse(json['timeOfDay'].split(':')[0]),
          minute: int.parse(json['timeOfDay'].split(':')[1]),
        ),
      );
}

class Activity {
  final String id;
  final String name;
  final String category;
  final String description;
  final List<String> tags;
  final bool isCompleted;
  final DateTime? completedDate;

  Activity({
    required this.id,
    required this.name,
    required this.category,
    required this.description,
    required this.tags,
    this.isCompleted = false,
    this.completedDate,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'category': category,
        'description': description,
        'tags': tags,
        'isCompleted': isCompleted,
        'completedDate': completedDate?.toIso8601String(),
      };

  factory Activity.fromJson(Map<String, dynamic> json) => Activity(
        id: json['id'],
        name: json['name'],
        category: json['category'],
        description: json['description'],
        tags: List<String>.from(json['tags']),
        isCompleted: json['isCompleted'],
        completedDate: json['completedDate'] != null
            ? DateTime.parse(json['completedDate'])
            : null,
      );

  Activity copyWith({
    String? id,
    String? name,
    String? category,
    String? description,
    List<String>? tags,
    bool? isCompleted,
    DateTime? completedDate,
  }) {
    return Activity(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      isCompleted: isCompleted ?? this.isCompleted,
      completedDate: completedDate ?? this.completedDate,
    );
  }
}

class Achievement {
  final String id;
  final String title;
  final String description;
  final String icon;
  final int requiredCount;
  final bool isUnlocked;
  final DateTime? unlockedDate;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.requiredCount,
    this.isUnlocked = false,
    this.unlockedDate,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'description': description,
        'icon': icon,
        'requiredCount': requiredCount,
        'isUnlocked': isUnlocked,
        'unlockedDate': unlockedDate?.toIso8601String(),
      };

  factory Achievement.fromJson(Map<String, dynamic> json) => Achievement(
        id: json['id'],
        title: json['title'],
        description: json['description'],
        icon: json['icon'],
        requiredCount: json['requiredCount'],
        isUnlocked: json['isUnlocked'],
        unlockedDate: json['unlockedDate'] != null
            ? DateTime.parse(json['unlockedDate'])
            : null,
      );
}

class UserProfile {
  final String name;
  final List<MoodEntry> moodHistory;
  final List<Activity> completedActivities;
  final List<Achievement> achievements;
  final Map<String, dynamic> preferences;
  final int totalActivitiesTried;
  final int currentStreak;

  UserProfile({
    required this.name,
    required this.moodHistory,
    required this.completedActivities,
    required this.achievements,
    required this.preferences,
    required this.totalActivitiesTried,
    required this.currentStreak,
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'moodHistory': moodHistory.map((e) => e.toJson()).toList(),
        'completedActivities':
            completedActivities.map((e) => e.toJson()).toList(),
        'achievements': achievements.map((e) => e.toJson()).toList(),
        'preferences': preferences,
        'totalActivitiesTried': totalActivitiesTried,
        'currentStreak': currentStreak,
      };

  factory UserProfile.fromJson(Map<String, dynamic> json) => UserProfile(
        name: json['name'],
        moodHistory: (json['moodHistory'] as List)
            .map((e) => MoodEntry.fromJson(e))
            .toList(),
        completedActivities: (json['completedActivities'] as List)
            .map((e) => Activity.fromJson(e))
            .toList(),
        achievements: (json['achievements'] as List)
            .map((e) => Achievement.fromJson(e))
            .toList(),
        preferences: json['preferences'],
        totalActivitiesTried: json['totalActivitiesTried'],
        currentStreak: json['currentStreak'],
      );
}
